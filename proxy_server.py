from flask import Flask, request, Response, jsonify
import requests
import json
import random
import time

app = Flask(__name__)

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    try:
        # 解析请求数据
        data = request.get_json()
        print(f"=== 收到请求 ===")
        print(f"请求头: {dict(request.headers)}")
        print(f"原始请求体: {json.dumps(data, ensure_ascii=False, indent=2)}")
        print("=" * 40)
        
        # 验证必要参数
        if not data or 'messages' not in data:
            return jsonify({"error": "缺少messages参数"}), 400
        
        # codebuddy.ai API配置
        url = "https://www.codebuddy.ai/v2/chat/completions"
        
        # 使用send_hello.py中的headers配置
        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "user-agent": "CodeBuddyIDE/0.1.13",
            "x-stainless-lang": "js",
            "x-stainless-package-version": "4.96.0",
            "x-stainless-os": "Windows",
            "x-stainless-arch": "x64",
            "x-stainless-runtime": "node",
            "x-stainless-runtime-version": "v20.19.0",
            "x-user-id": "9f31d4eb-d342-44a9-b914-3f09f28575c7",
            "authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJXVzhVVkZuS0lNSnl3cFdQWjBEWTZxeE9LQ2dpcVVjNXN3RHBkVjM1UUV3In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ds2gWj0-MfjmQS3TUlgZblZjSKPsiplg3ibtXWd7JUWlmE_b2ef_L-eVcjefv6NC-qDyWFNexlOte9roCutwn2QpFkQPzTHVKnqf5aboMkUfStagwa4DbH6oYdeWd7KeKzjYv0bUmrLauprKsSWp7u26zk2ycGxTqT5j8ZKO_3Q-UwL7H71APPOuveatbofMnsnPmcPAlmwP4PhYzc0w692DcqiivVig9G7yvT1gOkFwd_7SbLnZI2yfsK6B9u3KZiDMNsoGdbvYBLAlL7WB2iVNO01s40iHJPwVOx9duwfxfAqlbgr74ZwSs_yBU1pUutLAKePRhUrQCy8nRMBVKQ",  # 使用send_hello.py中的实际token
            "x-domain": "www.codebuddy.ai",
            "x-conversation-id": f"c-{str(random.randint(10000, 99999))}-{int(time.time() * 1000)}",
            "x-conversation-request-id": f"r-{str(random.randint(10000, 99999))}-{int(time.time() * 1000)}",
            "x-conversation-message-id": f"m-{str(random.randint(10000, 99999))}-{int(time.time() * 1000)}",
            "x-agent-intent": "ask",
            "x-stainless-retry-count": "0",
            "x-stainless-timeout": "600"
        }
        
        # 构建转发payload，使用请求中的messages
        payload = {
            "model": "claude-4.0",  # 固定使用claude-4.0
            "messages": data.get("messages", []),
            "max_tokens": data.get("max_tokens", 8192),
            "stream": True,  # codebuddy.ai只支持流式请求
            "stream_options": {
                "include_usage": True
            }
        }
        
        # 打印请求体到日志
        print(f"=== 发送请求到 codebuddy.ai ===")
        print(f"请求体: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        print("=" * 40)
        
        # 转发请求到codebuddy.ai
        response = requests.post(url, headers=headers, json=payload, stream=True)
        
        if response.status_code == 200:
            # codebuddy.ai只支持流式响应，需要转换为OpenAI格式
            def generate():
                for line in response.iter_lines():
                    if line:
                        decoded_line = line.decode('utf-8')
                        if decoded_line.startswith('data: '):
                            data_content = decoded_line[6:]  # 移除 'data: ' 前缀
                            if data_content == '[DONE]':
                                yield 'data: [DONE]\n\n'
                            else:
                                try:
                                    # 解析JSON并转换为OpenAI格式
                                    chunk_data = json.loads(data_content)
                                    
                                    # 转换为OpenAI标准格式
                                    openai_chunk = {
                                        "id": chunk_data.get("id", ""),
                                        "object": "chat.completion.chunk",
                                        "created": chunk_data.get("created", int(time.time())),
                                        "model": "Claude-4",  # 固定返回Claude-4
                                        "choices": chunk_data.get("choices", []),
                                        "usage": chunk_data.get("usage")
                                    }
                                    
                                    # 移除None值
                                    openai_chunk = {k: v for k, v in openai_chunk.items() if v is not None}
                                    
                                    # 确保JSON格式正确
                                    json_str = json.dumps(openai_chunk, ensure_ascii=False, separators=(',', ':'))
                                    yield f'data: {json_str}\n\n'
                                except json.JSONDecodeError:
                                    # 如果解析失败，直接返回原始数据
                                    yield decoded_line + '\n\n'
                        
            return Response(generate(), content_type='text/event-stream', headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no'
            })
        else:
            print(f"=== API请求失败 ===")
            print(f"状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            print("=" * 40)
            return jsonify({"error": f"API请求失败，状态码: {response.status_code}", "details": response.text}), response.status_code
            
    except Exception as e:
        return jsonify({"error": f"服务器错误: {str(e)}"}), 500

@app.route('/v1/models', methods=['GET'])
def list_models():
    # 返回固定的模型列表，符合OpenAI格式
    return jsonify({
        "object": "list",
        "data": [
            {
                "id": "Claude-4",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "codebuddy"
            }
        ]
    })

if __name__ == '__main__':
    print("OpenAI代理服务器启动中...")
    print("服务地址: http://localhost:3011")
    print("接口地址: http://localhost:3011/v1/chat/completions")
    print("固定Model: Claude-4.0")
    print("固定Authorization: sk-1")
    app.run(host='0.0.0.0', port=3011, debug=True)
