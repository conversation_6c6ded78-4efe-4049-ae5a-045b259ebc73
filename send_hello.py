import requests
import json
import random
import time

# API URL
url = "https://www.codebuddy.ai/v2/chat/completions"

# Headers (using info from 1.txt, with placeholder for authorization)
headers = {
    "accept": "application/json",
    "content-type": "application/json",
    "user-agent": "CodeBuddyIDE/0.1.13",
    "x-stainless-lang": "js",
    "x-stainless-package-version": "4.96.0",
    "x-stainless-os": "Windows",
    "x-stainless-arch": "x64",
    "x-stainless-runtime": "node",
    "x-stainless-runtime-version": "v20.19.0",
    "x-user-id": "9f31d4eb-d342-44a9-b914-3f09f28575c7",
    "authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJXVzhVVkZuS0lNSnl3cFdQWjBEWTZxeE9LQ2dpcVVjNXN3RHBkVjM1UUV3In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ds2gWj0-MfjmQS3TUlgZblZjSKPsiplg3ibtXWd7JUWlmE_b2ef_L-eVcjefv6NC-qDyWFNexlOte9roCutwn2QpFkQPzTHVKnqf5aboMkUfStagwa4DbH6oYdeWd7KeKzjYv0bUmrLauprKsSWp7u26zk2ycGxTqT5j8ZKO_3Q-UwL7H71APPOuveatbofMnsnPmcPAlmwP4PhYzc0w692DcqiivVig9G7yvT1gOkFwd_7SbLnZI2yfsK6B9u3KZiDMNsoGdbvYBLAlL7WB2iVNO01s40iHJPwVOx9duwfxfAqlbgr74ZwSs_yBU1pUutLAKePRhUrQCy8nRMBVKQ",  # 需要替换为实际的token
    "x-domain": "www.codebuddy.ai",
    "x-conversation-id": f"c-{str(random.randint(10000, 99999))}-{int(time.time() * 1000)}",
    "x-conversation-request-id": f"r-{str(random.randint(10000, 99999))}-{int(time.time() * 1000)}",
    "x-conversation-message-id": f"m-{str(random.randint(10000, 99999))}-{int(time.time() * 1000)}",
    "x-agent-intent": "ask",
    "x-stainless-retry-count": "0",
    "x-stainless-timeout": "600"
}

# Request payload - only sending "你好" message
payload = {
    "model": "claude-4.0",
    "messages": [
        {
            "role": "system",
            "content": "You are a helpful assistant."
        },
        {
            "role": "user",
            "content": "你好"
        }
    ],
    "max_tokens": 8192,
    "stream": True,
    "stream_options": {
        "include_usage": True
    }
}

try:
    # Send POST request
    response = requests.post(url, headers=headers, json=payload, stream=True)
    
    if response.status_code == 200:
        print("请求成功!")
        # 处理流式响应
        for line in response.iter_lines():
            if line:
                decoded_line = line.decode('utf-8')
                if decoded_line.startswith('data: '):
                    data = decoded_line[6:]  # 移除 'data: ' 前缀
                    if data != '[DONE]':
                        try:
                            json_data = json.loads(data)
                            print(json_data)
                        except json.JSONDecodeError:
                            print(f"无法解析JSON: {data}")
    else:
        print(f"请求失败，状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
except Exception as e:
    print(f"发生错误: {e}")
